import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import tailwindcss from '@tailwindcss/vite'

// https://vite.dev/config/
export default defineConfig({
  plugins: [
    tailwindcss(),
    react(),
  ],
  server: {
    port: 3456,        // 设置固定端口为 3456
    strictPort: true,  // 如果端口被占用，不会自动尝试下一个端口，而是直接报错
    host: true,        // 允许外部访问（可选）
    open: true         // 启动时自动打开浏览器（可选）
  }
})
