import { useCallback } from 'react';
import type { DownloadItem, DownloadProgress, AppState } from '../types';
import { DownloadStatus } from '../types';
import { saveDownloadedFile } from '../utils/downloadUtils';

interface UseDownloadControlProps {
  // 应用状态对象，包含下载数据、进度、控制器等信息
  state: AppState;
  // 更新下载进度的回调函数
  updateProgress: (requestId: string, progress: Partial<DownloadProgress[string]>) => void;
  // 标记文件为已保存状态的回调函数
  markFileAsSaved: (requestId: string) => void;
}

/**
 * 下载控制Hook
 * 负责下载的控制操作：保存、暂停、继续等
 */
export function useDownloadControl({
  state,
  updateProgress,
  markFileAsSaved
}: UseDownloadControlProps) {

  // 手动保存到本地
  const saveToLocal = useCallback(async () => {
    const item = state.downloadData;
    if (!item || !state.downloadBlobUrl) return;

    try {
      // 检查当前状态是否为已完成或已保存（允许重复保存）
      const progress = state.downloadProgress[item.requestId];
      if (!progress || (progress.status !== DownloadStatus.COMPLETED && progress.status !== DownloadStatus.SAVED)) {
        console.warn('文件尚未下载完成，无法保存');
        return;
      }

      // 使用 blob URL 保存文件
      await saveDownloadedFile(state.downloadBlobUrl, item.filename || 'download');

      // 标记为已保存
      markFileAsSaved(item.requestId);

      updateProgress(item.requestId, {
        status: DownloadStatus.SAVED,
        statusText: '已保存到本地',
        percentage: 100
      });

      console.log('文件已保存到本地:', item.filename);
    } catch (error) {
      console.error('保存失败:', error);
      updateProgress(item.requestId, {
        status: DownloadStatus.ERROR,
        statusText: '保存失败: ' + (error as Error).message,
        percentage: 0
      });
    }
  }, [state.downloadData, state.downloadBlobUrl, state.downloadProgress, updateProgress, markFileAsSaved]);

  // 关闭标签页
  const closeTab = useCallback(() => {
    window.close();
  }, []);

  // 检查是否可以保存
  const canSave = useCallback(() => {
    const item = state.downloadData;
    if (!item || !state.downloadBlobUrl) return false;

    const progress = state.downloadProgress[item.requestId];
    return progress && (progress.status === DownloadStatus.COMPLETED || progress.status === DownloadStatus.SAVED);
  }, [state.downloadData, state.downloadBlobUrl, state.downloadProgress]);

  // 获取当前下载状态 - 无状态时返回 null
  const getCurrentDownloadStatus = useCallback((): DownloadStatus | null => {
    const item = state.downloadData;
    if (!item) return null;

    const progress = state.downloadProgress[item.requestId];
    if (!progress) {
      // 检查是否已保存
      if (state.savedFiles.has(item.requestId)) {
        return DownloadStatus.SAVED;
      }
      return null; // 无进度信息且未保存时返回 null
    }

    return progress.status;
  }, [state.downloadData, state.downloadProgress, state.savedFiles]);

  // 重试下载
  const retryDownload = useCallback(async (startDownloadWithData: (data: DownloadItem) => Promise<void>) => {
    const item = state.downloadData;
    if (!item) return;

    console.log('重试下载');
    await startDownloadWithData(item);
  }, [state.downloadData]);

  return {
    saveToLocal,
    closeTab,
    canSave,
    getCurrentDownloadStatus,
    retryDownload
  };
}
